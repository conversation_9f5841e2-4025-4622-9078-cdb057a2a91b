/*
 * Chava Electricals Website - Store Page
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { useState } from 'react';
import StoreHero from '@/components/store/StoreHero';
import ProductCategories from '@/components/store/ProductCategories';
import ProductGrid from '@/components/store/ProductGrid';
import ShoppingCart from '@/components/store/ShoppingCart';

export default function StorePage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [cartItems, setCartItems] = useState([]);
  const [isCartOpen, setIsCartOpen] = useState(false);

  const addToCart = (product: any) => {
    setCartItems(prev => {
      const existingItem = prev.find((item: any) => item.id === product.id);
      if (existingItem) {
        return prev.map((item: any) =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }
      return [...prev, { ...product, quantity: 1 }];
    });
  };

  const removeFromCart = (productId: number) => {
    setCartItems(prev => prev.filter((item: any) => item.id !== productId));
  };

  const updateQuantity = (productId: number, quantity: number) => {
    if (quantity === 0) {
      removeFromCart(productId);
      return;
    }
    setCartItems(prev =>
      prev.map((item: any) =>
        item.id === productId ? { ...item, quantity } : item
      )
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHero cartItemsCount={cartItems.length} onCartClick={() => setIsCartOpen(true)} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <ProductCategories 
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
        />
        
        <ProductGrid 
          selectedCategory={selectedCategory}
          onAddToCart={addToCart}
        />
      </div>

      <ShoppingCart
        isOpen={isCartOpen}
        onClose={() => setIsCartOpen(false)}
        items={cartItems}
        onRemoveItem={removeFromCart}
        onUpdateQuantity={updateQuantity}
      />
    </div>
  );
}
